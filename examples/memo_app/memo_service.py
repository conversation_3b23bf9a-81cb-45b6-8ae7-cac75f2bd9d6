#!/usr/bin/env python3
"""
Memo Service runner.
This script starts the memo service that can be accessed by multiple nodes.
"""

import sys
import os
import signal
import time

# Add parent directory to path to import DistributedBus
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from DistributedBus import DistributedBus
from memo_manager import MemoManager


def signal_handler(signum, frame):
    """Handle shutdown signals."""
    print("\nShutting down memo service...")
    sys.exit(0)


def main():
    """Main function to run the memo service."""
    print("Starting Memo Service...")
    
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Initialize DistributedBus
        bus = DistributedBus().start()
        
        # Create memo manager instance
        memo_manager = MemoManager()
        memo_manager.set_bus(bus)
        
        # The service is automatically registered due to @service decorator
        # But we can also manually register it if needed:
        # bus.register_service("MemoService", memo_manager)
        
        print("Memo Service is running...")
        print("Available services:", list(bus.list_services().keys()))
        print("Local services:", bus.get_node_info()["local_services"])
        print("Press Ctrl+C to stop")
        
        # Keep the service running
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            pass
        
    except Exception as e:
        print(f"Error starting memo service: {e}")
        return 1
    
    finally:
        print("Memo service stopped")
        if 'bus' in locals():
            bus.close()
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
