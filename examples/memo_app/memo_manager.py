"""
Memo Manager service for the memo application.
Provides CRUD operations for memo management with persistent JSON storage.
"""

import json
import os
import time
import uuid
import sys
from typing import List, Dict, Optional

# Add parent directory to path to import DistributedBus
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from DistributedBus import service, DistributedBus


@service(name="MemoService")
class MemoManager:
    """Memo management service with persistent storage."""
    
    def __init__(self, storage_file: str = "memos.json"):
        self.storage_file = storage_file
        self.memos: Dict[str, Dict] = {}
        self.bus = None
        self._load_memos()
    
    def set_bus(self, bus: DistributedBus):
        """Set the distributed bus instance for event publishing."""
        self.bus = bus
    
    def _load_memos(self):
        """Load memos from storage file."""
        if os.path.exists(self.storage_file):
            try:
                with open(self.storage_file, 'r', encoding='utf-8') as f:
                    self.memos = json.load(f)
                print(f"Loaded {len(self.memos)} memos from {self.storage_file}")
            except Exception as e:
                print(f"Error loading memos: {e}")
                self.memos = {}
        else:
            self.memos = {}
    
    def _save_memos(self):
        """Save memos to storage file."""
        try:
            with open(self.storage_file, 'w', encoding='utf-8') as f:
                json.dump(self.memos, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving memos: {e}")
    
    def add_memo(self, title: str, content: str, tags: List[str] = None) -> str:
        """
        Add a new memo.
        
        Args:
            title: Memo title
            content: Memo content
            tags: Optional list of tags
            
        Returns:
            Memo ID
        """
        memo_id = str(uuid.uuid4())
        memo = {
            "id": memo_id,
            "title": title,
            "content": content,
            "tags": tags or [],
            "created_at": time.time(),
            "updated_at": time.time()
        }
        
        self.memos[memo_id] = memo
        self._save_memos()
        
        # Publish event if bus is available
        if self.bus:
            self.bus.publish_event("memo_added", {
                "memo_id": memo_id,
                "title": title,
                "content": content,
                "tags": tags or [],
                "timestamp": time.time()
            })
        
        print(f"Added memo: {title}")
        return memo_id
    
    def get_memo(self, memo_id: str) -> Optional[Dict]:
        """
        Get a memo by ID.
        
        Args:
            memo_id: Memo ID
            
        Returns:
            Memo data or None if not found
        """
        return self.memos.get(memo_id)
    
    def get_all_memos(self) -> List[Dict]:
        """
        Get all memos.
        
        Returns:
            List of all memos
        """
        return list(self.memos.values())
    
    def update_memo(self, memo_id: str, title: str = None, content: str = None, tags: List[str] = None) -> bool:
        """
        Update an existing memo.
        
        Args:
            memo_id: Memo ID
            title: New title (optional)
            content: New content (optional)
            tags: New tags (optional)
            
        Returns:
            True if updated, False if memo not found
        """
        if memo_id not in self.memos:
            return False
        
        memo = self.memos[memo_id]
        
        if title is not None:
            memo["title"] = title
        if content is not None:
            memo["content"] = content
        if tags is not None:
            memo["tags"] = tags
        
        memo["updated_at"] = time.time()
        
        self._save_memos()
        
        # Publish event if bus is available
        if self.bus:
            self.bus.publish_event("memo_changed", {
                "memo_id": memo_id,
                "title": memo["title"],
                "content": memo["content"],
                "tags": memo["tags"],
                "timestamp": time.time()
            })
        
        print(f"Updated memo: {memo['title']}")
        return True
    
    def delete_memo(self, memo_id: str) -> bool:
        """
        Delete a memo.
        
        Args:
            memo_id: Memo ID
            
        Returns:
            True if deleted, False if memo not found
        """
        if memo_id not in self.memos:
            return False
        
        memo = self.memos[memo_id]
        del self.memos[memo_id]
        self._save_memos()
        
        # Publish event if bus is available
        if self.bus:
            self.bus.publish_event("memo_deleted", {
                "memo_id": memo_id,
                "title": memo["title"],
                "timestamp": time.time()
            })
        
        print(f"Deleted memo: {memo['title']}")
        return True
    
    def search_memos(self, query: str) -> List[Dict]:
        """
        Search memos by title or content.
        
        Args:
            query: Search query
            
        Returns:
            List of matching memos
        """
        query_lower = query.lower()
        results = []
        
        for memo in self.memos.values():
            if (query_lower in memo["title"].lower() or 
                query_lower in memo["content"].lower() or
                any(query_lower in tag.lower() for tag in memo["tags"])):
                results.append(memo)
        
        return results
    
    def get_memos_by_tag(self, tag: str) -> List[Dict]:
        """
        Get memos by tag.
        
        Args:
            tag: Tag to search for
            
        Returns:
            List of memos with the specified tag
        """
        results = []
        for memo in self.memos.values():
            if tag in memo["tags"]:
                results.append(memo)
        
        return results
