"""
Main DistributedBus class that integrates all components.
"""

import time
from typing import Any, Optional, Callable

from .personal_ca import PersonalCA
from .service_discovery import ServiceDiscovery
from .rpc_framework import RPCServer, RPCClient, get_service_registry
from .event_system import EventSystem


class DistributedBus:
    """
    Main class for the Distributed Bus framework.
    
    Provides service discovery, RPC mechanisms, and event delivery
    for multi-personal-node applications.
    """
    
    def __init__(self, ca_dir: str = "~/.distributed_bus_ca"):
        """
        Initialize the Distributed Bus.
        
        Args:
            ca_dir: Directory for personal CA certificates
        """
        self.personal_ca = PersonalCA(ca_dir)
        self.service_discovery = None
        self.rpc_server = None
        self.rpc_client = None
        self.event_system = None
        self._started = False
    
    def start(self):
        """Start the distributed bus services."""
        if self._started:
            return self
        
        print("Starting Distributed Bus...")
        
        # Initialize service discovery
        self.service_discovery = ServiceDiscovery(self.personal_ca)
        
        # Initialize RPC components
        self.rpc_server = RPCServer(self.personal_ca, self.service_discovery)
        self.rpc_client = RPCClient(self.personal_ca, self.service_discovery)
        
        # Initialize event system
        self.event_system = EventSystem(self.personal_ca)
        
        # Register services marked with @service decorator
        self._register_decorated_services()
        
        self._started = True
        print("Distributed Bus started successfully")
        
        return self
    
    def _register_decorated_services(self):
        """Register services that were marked with @service decorator."""
        service_registry = get_service_registry()
        for service_name, service_registration in service_registry.items():
            try:
                # Create service instance
                service_instance = service_registration.create_instance()
                
                # Register with RPC server
                self.rpc_server.register_service(service_name, service_instance)
                
                print(f"Auto-registered service: {service_name}")
                
            except Exception as e:
                print(f"Failed to auto-register service {service_name}: {e}")
    
    def lookup(self, service_name: str, timeout: float = 5.0) -> Optional[Any]:
        """
        Look up a service by name.
        
        Args:
            service_name: Name of the service to look up
            timeout: Maximum time to wait for service discovery
            
        Returns:
            Service proxy object or None if not found
        """
        if not self._started:
            raise RuntimeError("DistributedBus not started. Call start() first.")
        
        # First check if it's a local service
        if service_name in self.rpc_server.services:
            return self.rpc_server.services[service_name]
        
        # Look up remote service
        proxy = self.rpc_client.lookup_service(service_name, timeout)
        if proxy:
            return proxy
        
        print(f"Service '{service_name}' not found")
        return None
    
    def register_service(self, service_name: str, service_instance: Any):
        """
        Manually register a service instance.
        
        Args:
            service_name: Name of the service
            service_instance: Instance of the service class
        """
        if not self._started:
            raise RuntimeError("DistributedBus not started. Call start() first.")
        
        self.rpc_server.register_service(service_name, service_instance)
    
    def unregister_service(self, service_name: str):
        """
        Unregister a service.
        
        Args:
            service_name: Name of the service to unregister
        """
        if not self._started:
            return
        
        self.rpc_server.unregister_service(service_name)
    
    def publish_event(self, event_name: str, data: Any):
        """
        Publish an event to all subscribers.
        
        Args:
            event_name: Name of the event
            data: Event data
        """
        if not self._started:
            raise RuntimeError("DistributedBus not started. Call start() first.")
        
        self.event_system.publish_event(event_name, data)
    
    def subscribe(self, event_name: str, handler: Callable):
        """
        Subscribe to an event.
        
        Args:
            event_name: Name of the event to subscribe to
            handler: Function to call when event is received
        """
        if not self._started:
            raise RuntimeError("DistributedBus not started. Call start() first.")
        
        self.event_system.subscribe(event_name, handler)
    
    def unsubscribe(self, event_name: str, handler: Callable):
        """
        Unsubscribe from an event.
        
        Args:
            event_name: Name of the event
            handler: Handler function to remove
        """
        if not self._started:
            return
        
        self.event_system.unsubscribe(event_name, handler)
    
    def list_services(self):
        """List all discovered services."""
        if not self._started:
            return {}
        
        return self.service_discovery.list_services()
    
    def get_node_info(self):
        """Get information about this node."""
        return {
            "ca_fingerprint": self.personal_ca.get_ca_fingerprint(),
            "started": self._started,
            "local_services": list(self.rpc_server.services.keys()) if self.rpc_server else [],
            "discovered_services": list(self.service_discovery.services.keys()) if self.service_discovery else []
        }
    
    def wait_for_service(self, service_name: str, timeout: float = 30.0) -> Optional[Any]:
        """
        Wait for a service to become available.
        
        Args:
            service_name: Name of the service to wait for
            timeout: Maximum time to wait
            
        Returns:
            Service proxy object or None if timeout
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            service = self.lookup(service_name, timeout=1.0)
            if service:
                return service
            time.sleep(0.5)
        
        return None
    
    def close(self):
        """Close the distributed bus and clean up resources."""
        if not self._started:
            return
        
        print("Shutting down Distributed Bus...")
        
        if self.event_system:
            self.event_system.close()
        
        if self.rpc_server:
            self.rpc_server.close()
        
        if self.service_discovery:
            self.service_discovery.close()
        
        self._started = False
        print("Distributed Bus shut down")
    
    def __enter__(self):
        """Context manager entry."""
        return self.start()
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()
