#!/usr/bin/env python3
"""
Demo script showing how to use the memo application.
This demonstrates the conversion from single-node to MPN application.
"""

import sys
import os
import time

# Add parent directory to path to import DistributedBus
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from DistributedBus import DistributedBus


def demo_original_style():
    """Show how the original single-node application would work."""
    print("=== Original Single-Node Style ===")
    print("# Before:")
    print("# memo_manager = MemoManager()")
    print()
    
    # This is how it would be done in single-node application
    from memo_manager import MemoManager
    
    # Create local instance (this is the old way)
    local_memo_manager = MemoManager("demo_local.json")
    
    # Use the service
    memo_id = local_memo_manager.add_memo("Local Test", "This is a local memo", ["demo", "local"])
    memos = local_memo_manager.get_all_memos()
    print(f"Local memos: {len(memos)}")
    
    print("Single-node demo completed\n")


def demo_mpn_style():
    """Show how the MPN application works."""
    print("=== Multi-Personal-Node Style ===")
    print("# CHANGE 1: Initialize DistributedBus")
    print("# for multi-node operation instead of single-node")
    print()
    
    # CHANGE 1: Initialize DistributedBus for multi-node operation
    bus = DistributedBus().start()
    
    print("# CHANGE 2: Get local or remote service")
    print("# instead of creating local instance")
    print()
    
    # CHANGE 2: Get local or remote service instead of creating local instance
    memo_manager = bus.lookup("MemoService")
    
    if not memo_manager:
        print("MemoService not found. Please start the memo service first:")
        print("python examples/memo_app/memo_service.py")
        return
    
    print("# CHANGE 3: Add annotation")
    print("# @service(name=\"MemoService\")")
    print("# class MemoManager:")
    print("#     ...")
    print()
    
    print("# Original CRUD code works unchanged:")
    print()
    
    # Original CRUD code works unchanged
    memos = memo_manager.get_all_memos()
    print(f"memo_manager.get_all_memos() -> {len(memos)} memos")
    
    memo_id = memo_manager.add_memo("MPN Test", "This is a multi-node memo", ["demo", "mpn"])
    print(f"memo_manager.add_memo(...) -> {memo_id}")
    
    memo_manager.update_memo(memo_id, content="Updated content for multi-node memo")
    print(f"memo_manager.update_memo({memo_id[:8]}..., content=...)")
    
    updated_memo = memo_manager.get_memo(memo_id)
    print(f"memo_manager.get_memo({memo_id[:8]}...) -> {updated_memo['title']}")
    
    # memo_manager.delete_memo(memo_id)
    # print(f"memo_manager.delete_memo({memo_id[:8]}...)")
    
    print("\nMPN demo completed")
    
    # Show node information
    node_info = bus.get_node_info()
    print(f"\nNode Info:")
    print(f"  CA Fingerprint: {node_info['ca_fingerprint'][:16]}...")
    print(f"  Local Services: {node_info['local_services']}")
    print(f"  Discovered Services: {node_info['discovered_services']}")
    
    bus.close()


def main():
    """Main demo function."""
    print("Memo Application Demo")
    print("====================")
    print()
    
    print("This demo shows how to convert a single-node application")
    print("into a Multi-Personal-Node (MPN) application with just 3 changes:")
    print()
    
    # Show original style
    demo_original_style()
    
    # Show MPN style
    demo_mpn_style()
    
    print("\nDemo completed!")
    print("\nTo run the full application:")
    print("1. Start the service: python examples/memo_app/memo_service.py")
    print("2. Start the GUI: python examples/memo_app/memo_gui.py")


if __name__ == "__main__":
    main()
