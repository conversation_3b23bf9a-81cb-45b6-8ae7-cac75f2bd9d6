"""
Simple GUI for the memo application using tkinter.
Demonstrates how to convert a single-node application to MPN application.
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import threading
import time
import sys
import os

# Add parent directory to path to import DistributedBus
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from DistributedBus import DistributedBus, event_subscriber


class MemoApp:
    """Simple memo application with GUI."""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Memo Application - Multi-Personal-Node")
        self.root.geometry("800x600")
        
        # Initialize DistributedBus for multi-node operation
        self.bus = DistributedBus().start()
        
        # Get local or remote service instead of creating local instance
        self.memo_manager = None
        self._connect_to_service()
        
        self.setup_ui()
        self.refresh_memos()
        
        # Setup event handlers
        self.setup_event_handlers()
    
    def _connect_to_service(self):
        """Connect to the memo service."""
        print("Looking for MemoService...")
        self.memo_manager = self.bus.wait_for_service("MemoService", timeout=10.0)
        
        if not self.memo_manager:
            messagebox.showerror("Error", "MemoService not found. Please start the memo service first.")
            self.root.quit()
            return
        
        print("Connected to MemoService")
    
    def setup_ui(self):
        """Setup the user interface."""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Memo Application", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 10))
        
        # Memo list
        list_frame = ttk.LabelFrame(main_frame, text="Memos", padding="5")
        list_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # Treeview for memo list
        columns = ("Title", "Tags", "Created", "Updated")
        self.memo_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=10)
        
        # Configure columns
        self.memo_tree.heading("Title", text="Title")
        self.memo_tree.heading("Tags", text="Tags")
        self.memo_tree.heading("Created", text="Created")
        self.memo_tree.heading("Updated", text="Updated")
        
        self.memo_tree.column("Title", width=300)
        self.memo_tree.column("Tags", width=150)
        self.memo_tree.column("Created", width=100)
        self.memo_tree.column("Updated", width=100)
        
        # Scrollbar for treeview
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.memo_tree.yview)
        self.memo_tree.configure(yscrollcommand=scrollbar.set)
        
        self.memo_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # Bind double-click to view memo
        self.memo_tree.bind("<Double-1>", self.view_memo)
        
        # Buttons frame
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=3, pady=(0, 10))
        
        # Buttons
        ttk.Button(button_frame, text="Add Memo", command=self.add_memo).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="View/Edit", command=self.view_memo).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Delete", command=self.delete_memo).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Refresh", command=self.refresh_memos).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Search", command=self.search_memos).pack(side=tk.LEFT, padx=(0, 5))
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E))
    
    def setup_event_handlers(self):
        """Setup event handlers for memo changes."""
        self.bus.subscribe("memo_added", self.on_memo_added)
        self.bus.subscribe("memo_changed", self.on_memo_changed)
        self.bus.subscribe("memo_deleted", self.on_memo_deleted)
    
    def on_memo_added(self, event):
        """Handle memo added event."""
        self.root.after(0, lambda: self.status_var.set(f"Memo added: {event.data['title']}"))
        self.root.after(100, self.refresh_memos)
    
    def on_memo_changed(self, event):
        """Handle memo changed event."""
        self.root.after(0, lambda: self.status_var.set(f"Memo updated: {event.data['title']}"))
        self.root.after(100, self.refresh_memos)
    
    def on_memo_deleted(self, event):
        """Handle memo deleted event."""
        self.root.after(0, lambda: self.status_var.set(f"Memo deleted: {event.data['title']}"))
        self.root.after(100, self.refresh_memos)
    
    def refresh_memos(self):
        """Refresh the memo list."""
        try:
            # Clear existing items
            for item in self.memo_tree.get_children():
                self.memo_tree.delete(item)
            
            # Get all memos
            memos = self.memo_manager.get_all_memos()
            
            # Add memos to tree
            for memo in memos:
                created = time.strftime("%Y-%m-%d", time.localtime(memo["created_at"]))
                updated = time.strftime("%Y-%m-%d", time.localtime(memo["updated_at"]))
                tags = ", ".join(memo["tags"])
                
                self.memo_tree.insert("", tk.END, values=(
                    memo["title"],
                    tags,
                    created,
                    updated
                ), tags=(memo["id"],))
            
            self.status_var.set(f"Loaded {len(memos)} memos")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to refresh memos: {e}")
    
    def add_memo(self):
        """Add a new memo."""
        dialog = MemoDialog(self.root, "Add Memo")
        if dialog.result:
            try:
                memo_id = self.memo_manager.add_memo(
                    dialog.result["title"],
                    dialog.result["content"],
                    dialog.result["tags"]
                )
                self.status_var.set(f"Added memo: {dialog.result['title']}")
                self.refresh_memos()
            except Exception as e:
                messagebox.showerror("Error", f"Failed to add memo: {e}")
    
    def view_memo(self, event=None):
        """View/edit selected memo."""
        selection = self.memo_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a memo to view.")
            return
        
        # Get memo ID from tags
        item = selection[0]
        memo_id = self.memo_tree.item(item)["tags"][0]
        
        try:
            memo = self.memo_manager.get_memo(memo_id)
            if not memo:
                messagebox.showerror("Error", "Memo not found.")
                return
            
            dialog = MemoDialog(self.root, "Edit Memo", memo)
            if dialog.result:
                success = self.memo_manager.update_memo(
                    memo_id,
                    dialog.result["title"],
                    dialog.result["content"],
                    dialog.result["tags"]
                )
                if success:
                    self.status_var.set(f"Updated memo: {dialog.result['title']}")
                    self.refresh_memos()
                else:
                    messagebox.showerror("Error", "Failed to update memo.")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to view memo: {e}")
    
    def delete_memo(self):
        """Delete selected memo."""
        selection = self.memo_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a memo to delete.")
            return
        
        # Get memo ID from tags
        item = selection[0]
        memo_id = self.memo_tree.item(item)["tags"][0]
        
        try:
            memo = self.memo_manager.get_memo(memo_id)
            if not memo:
                messagebox.showerror("Error", "Memo not found.")
                return
            
            if messagebox.askyesno("Confirm", f"Delete memo '{memo['title']}'?"):
                success = self.memo_manager.delete_memo(memo_id)
                if success:
                    self.status_var.set(f"Deleted memo: {memo['title']}")
                    self.refresh_memos()
                else:
                    messagebox.showerror("Error", "Failed to delete memo.")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to delete memo: {e}")
    
    def search_memos(self):
        """Search memos."""
        query = simpledialog.askstring("Search", "Enter search query:")
        if query:
            try:
                results = self.memo_manager.search_memos(query)
                
                # Clear existing items
                for item in self.memo_tree.get_children():
                    self.memo_tree.delete(item)
                
                # Add search results to tree
                for memo in results:
                    created = time.strftime("%Y-%m-%d", time.localtime(memo["created_at"]))
                    updated = time.strftime("%Y-%m-%d", time.localtime(memo["updated_at"]))
                    tags = ", ".join(memo["tags"])
                    
                    self.memo_tree.insert("", tk.END, values=(
                        memo["title"],
                        tags,
                        created,
                        updated
                    ), tags=(memo["id"],))
                
                self.status_var.set(f"Found {len(results)} memos matching '{query}'")
                
            except Exception as e:
                messagebox.showerror("Error", f"Search failed: {e}")
    
    def run(self):
        """Run the application."""
        try:
            self.root.mainloop()
        finally:
            self.bus.close()


class MemoDialog:
    """Dialog for adding/editing memos."""
    
    def __init__(self, parent, title, memo=None):
        self.result = None
        
        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x400")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center the dialog
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))
        
        self.setup_ui(memo)
        
        # Wait for dialog to close
        self.dialog.wait_window()
    
    def setup_ui(self, memo):
        """Setup dialog UI."""
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        ttk.Label(main_frame, text="Title:").pack(anchor=tk.W)
        self.title_var = tk.StringVar(value=memo["title"] if memo else "")
        title_entry = ttk.Entry(main_frame, textvariable=self.title_var, width=50)
        title_entry.pack(fill=tk.X, pady=(0, 10))
        title_entry.focus()
        
        # Content
        ttk.Label(main_frame, text="Content:").pack(anchor=tk.W)
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        self.content_text = tk.Text(content_frame, wrap=tk.WORD, height=10)
        content_scrollbar = ttk.Scrollbar(content_frame, orient=tk.VERTICAL, command=self.content_text.yview)
        self.content_text.configure(yscrollcommand=content_scrollbar.set)
        
        self.content_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        content_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        if memo:
            self.content_text.insert(tk.END, memo["content"])
        
        # Tags
        ttk.Label(main_frame, text="Tags (comma-separated):").pack(anchor=tk.W)
        self.tags_var = tk.StringVar(value=", ".join(memo["tags"]) if memo else "")
        tags_entry = ttk.Entry(main_frame, textvariable=self.tags_var, width=50)
        tags_entry.pack(fill=tk.X, pady=(0, 10))
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="OK", command=self.ok_clicked).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="Cancel", command=self.cancel_clicked).pack(side=tk.RIGHT)
    
    def ok_clicked(self):
        """Handle OK button click."""
        title = self.title_var.get().strip()
        content = self.content_text.get("1.0", tk.END).strip()
        tags_text = self.tags_var.get().strip()
        
        if not title:
            messagebox.showwarning("Warning", "Please enter a title.")
            return
        
        tags = [tag.strip() for tag in tags_text.split(",") if tag.strip()]
        
        self.result = {
            "title": title,
            "content": content,
            "tags": tags
        }
        
        self.dialog.destroy()
    
    def cancel_clicked(self):
        """Handle Cancel button click."""
        self.dialog.destroy()


if __name__ == "__main__":
    app = MemoApp()
    app.run()
