"""
Decorators for service registration and event subscription.
"""

from typing import Callable, Any
from .rpc_framework import get_service_registry, ServiceRegistration


def service(name: str):
    """
    Decorator to mark a class as an RPC service.
    
    Args:
        name: The name of the service for discovery
    
    Example:
        @service(name="FileService")
        class FileService:
            def open(self, filename, mode):
                return open(filename, mode).read()
    """
    def decorator(cls):
        # Register the service class
        registry = get_service_registry()
        registry[name] = ServiceRegistration(name, cls)
        
        # Add service metadata to the class
        cls._service_name = name
        cls._is_distributed_service = True
        
        return cls
    
    return decorator


# Global registry for event subscribers
_event_subscribers = {}


def event_subscriber(event_name: str):
    """
    Decorator to mark a function as an event subscriber.
    
    Args:
        event_name: The name of the event to subscribe to
    
    Example:
        @event_subscriber("data_changed")
        def handle_data_change(event):
            print(f"Data changed: {event.data}")
    """
    def decorator(func: Callable):
        # Register the event subscriber
        if event_name not in _event_subscribers:
            _event_subscribers[event_name] = []
        
        _event_subscribers[event_name].append(func)
        
        # Add metadata to the function
        func._event_name = event_name
        func._is_event_subscriber = True
        
        return func
    
    return decorator


def get_event_subscribers():
    """Get the global event subscribers registry."""
    return _event_subscribers


class EventData:
    """Container for event data."""
    
    def __init__(self, event_name: str, data: Any, timestamp: float, source_node: str = None):
        self.event_name = event_name
        self.data = data
        self.timestamp = timestamp
        self.source_node = source_node
