#!/usr/bin/env python3
"""
Simple test to verify the DistributedBus framework is working.
"""

import sys
import os
import time
import threading

# Add parent directory to path to import DistributedBus
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from DistributedBus import DistributedBus, service, event_subscriber


@service(name="TestService")
class TestService:
    """Simple test service."""
    
    def __init__(self):
        self.counter = 0
    
    def hello(self, name):
        """Say hello."""
        self.counter += 1
        return f"Hello {name}! (call #{self.counter})"
    
    def get_counter(self):
        """Get the call counter."""
        return self.counter


@event_subscriber("test_event")
def handle_test_event(event):
    """Handle test events."""
    print(f"Received event: {event.event_name} with data: {event.data}")


def test_service_and_events():
    """Test service registration and event delivery."""
    print("Testing DistributedBus Framework")
    print("================================")
    
    # Start the distributed bus
    print("1. Starting DistributedBus...")
    bus = DistributedBus().start()
    
    # The TestService should be automatically registered due to @service decorator
    print("2. Looking up TestService...")
    test_service = bus.lookup("TestService", timeout=2.0)
    
    if test_service:
        print("3. Testing RPC calls...")
        result1 = test_service.hello("Alice")
        print(f"   test_service.hello('Alice') -> {result1}")
        
        result2 = test_service.hello("Bob")
        print(f"   test_service.hello('Bob') -> {result2}")
        
        counter = test_service.get_counter()
        print(f"   test_service.get_counter() -> {counter}")
    else:
        print("   TestService not found!")
    
    print("4. Testing event publishing...")
    bus.publish_event("test_event", {"message": "Hello from test!", "timestamp": time.time()})
    
    # Wait a bit for event to be processed
    time.sleep(0.5)
    
    print("5. Node information:")
    node_info = bus.get_node_info()
    print(f"   CA Fingerprint: {node_info['ca_fingerprint'][:16]}...")
    print(f"   Local Services: {node_info['local_services']}")
    print(f"   Discovered Services: {node_info['discovered_services']}")
    
    print("6. Shutting down...")
    bus.close()
    
    print("Test completed successfully!")


if __name__ == "__main__":
    test_service_and_events()
